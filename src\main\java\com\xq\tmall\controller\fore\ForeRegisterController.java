package com.xq.tmall.controller.fore;

import com.alibaba.fastjson.JSONObject;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.entity.Address;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.AddressService;
import com.xq.tmall.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前台趣味商城-用户注册
 */
@Api(tags = "前台趣味商城-用户注册API")
@RestController
@RequiredArgsConstructor
@Slf4j
public class ForeRegisterController extends BaseController {
    private final AddressService addressService;
    private final UserService userService;

    // 获取注册页面地址数据-API接口
    @ApiOperation(value = "获取注册页面地址数据", notes = "获取注册页面地址数据")
    @GetMapping(value = "api/addresses", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Map<String, Object>> getRegisterAddresses() {
        try {
            // 获取省份信息
            List<Address> addressList = addressService.getRoot();
            // 获取默认的市级地址信息（北京市）
            List<Address> cityList = addressService.getList(null, "110000");
            // 获取默认的区级地址信息（东城区）
            List<Address> districtList = addressService.getList(null, "110100");

            Map<String, Object> data = new HashMap<>();
            data.put("addressList", addressList);
            data.put("cityList", cityList);
            data.put("districtList", districtList);
            data.put("defaultAddressId", "110000");
            data.put("defaultCityAddressId", "110100");
            data.put("defaultDistrictAddressId", "110101");

            return Result.success("获取地址数据成功", data);
        } catch (Exception e) {
            log.error("获取注册页面地址数据失败", e);
            return Result.error("获取地址数据失败：" + e.getMessage());
        }
    }

    // 根据上级地址ID获取下级地址列表-API接口
    @ApiOperation(value = "根据上级地址ID获取下级地址列表", notes = "根据上级地址ID获取下级地址列表")
    @GetMapping(value = "api/addresses/{parentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<Address>> getAddressesByParentId(@PathVariable("parentId") String parentId) {
        try {
            List<Address> addressList = addressService.getList(null, parentId);
            return Result.success("获取地址列表成功", addressList);
        } catch (Exception e) {
            log.error("获取地址列表失败，父级ID: {}", parentId, e);
            return Result.error("获取地址列表失败：" + e.getMessage());
        }
    }

    // 天猫前台-用户注册-API接口
    @ApiOperation(value = "用户注册", notes = "用户注册")
    @PostMapping(value = "api/auth/register", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Map<String, Object>> register(
            @RequestParam(value = "user_name") String user_name  /*用户名 */,
            @RequestParam(value = "user_nickname") String user_nickname  /*用户昵称 */,
            @RequestParam(value = "user_password") String user_password  /*用户密码*/,
            @RequestParam(value = "user_gender") String user_gender  /*用户性别*/,
            @RequestParam(value = "user_birthday") String user_birthday /*用户生日*/,
            @RequestParam(value = "user_address") String user_address  /*用户所在地 */
    ) {
        try {
            // 验证参数
            if (user_name == null || user_name.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            if (user_password == null || user_password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }
            
            // 验证用户名是否存在
            User user1 = new User();
            user1.setUser_name(user_name);
            Integer count = userService.getTotal(user1);
            if (count > 0) {
                // 用户名已存在，返回错误信息
                return Result.error("用户名已存在，请重新输入");
            }
            
            // 创建用户对象
            User user = new User();
            user.setUser_name(user_name);
            user.setUser_nickname(user_nickname);
            user.setUser_password(user_password);
            user.setUser_gender(Byte.valueOf(user_gender));
            user.setUser_birthday(user_birthday);
            // 地址对象
            Address address = new Address();
            address.setAddress_areaId(user_address);
            user.setUser_address(address);
            user.setUser_homeplace(address);
            user.setUser_realname(user_name);
            user.setDel_flag(0);
            
            // 用户注册
            if (userService.add(user)) {
                // 注册成功
                Map<String, Object> data = new HashMap<>();
                data.put("userId", user.getUser_id());
                data.put("userName", user.getUser_name());
                return Result.success("注册成功", data);
            } else {
                return Result.error("注册失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return Result.error("注册失败：" + e.getMessage());
        }
    }
}
