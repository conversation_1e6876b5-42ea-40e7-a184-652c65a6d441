<%@ page contentType="text/html;charset=UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link href="${pageContext.request.contextPath}/res/css/bootstrap.min.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/res/css/bootstrap-select.min.css" rel="stylesheet"/>
    <link href="${pageContext.request.contextPath}/res/css/fore/fore_main.css" rel="stylesheet"/>
    <script src="${pageContext.request.contextPath}/res/js/jquery-1.11.3.min.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/bootstrap.min.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/bootstrap-select.min.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/defaults-zh_CN.min.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/base.js"></script>
    <script src="${pageContext.request.contextPath}/res/js/fore/api.js"></script>
</head>