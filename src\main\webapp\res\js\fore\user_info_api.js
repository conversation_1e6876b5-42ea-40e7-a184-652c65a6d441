/**
 * 用户信息页面API数据处理模块
 */
var UserInfoAPI = {
    
    // 当前用户信息
    currentUser: null,
    
    // 初始化
    init: function() {
        this.bindEvents();

        // 延迟加载用户信息，避免与导航栏API调用冲突
        var self = this;
        setTimeout(function() {
            self.loadUserInfo();
        }, 500);
    },
    
    // 绑定事件
    bindEvents: function() {
        var self = this;
        
        // 绑定表单提交事件
        $("#register_form").off('submit').on('submit', function(e) {
            e.preventDefault();
            self.handleUpdateUserInfo();
            return false;
        });
        
        // 绑定头像上传事件
        $("#user_profile_picture_src").off('change').on('change', function() {
            self.handleAvatarUpload(this);
        });
        
        // 绑定地址选择事件
        $('#select_user_address_province').off('change').on('change', function() {
            self.loadCityList($(this).val());
        });
        
        $('#select_user_address_city').off('change').on('change', function() {
            self.loadDistrictList($(this).val());
        });
        
        // 绑定输入框焦点事件
        $(".form-text").off('focus').on('focus', function() {
            $(this).css("border", "1px solid #3879D9")
                .next().css("display", "none");
        });
        
        $(".form-text").off('blur').on('blur', function() {
            $(this).css("border-color", "#cccccc");
        });
    },
    
    // 加载用户信息
    loadUserInfo: function() {
        var self = this;

        API.getUserInfo(function(data) {
            self.currentUser = data.user;
            self.populateUserForm(data);
        }, function(message, code) {
            self.showError('获取用户信息失败：' + message);
            if (code === 401) {
                // 未登录，跳转到登录页
                window.location.href = API.baseUrl + '/login';
            }
        });
    },
    
    // 填充用户表单
    populateUserForm: function(data) {
        var user = data.user;
        
        // 填充基本信息
        $("#user_nickname").val(user.user_nickname || '');
        $("#user_realname").val(user.user_realname || '');
        $("#user_birthday").val(user.user_birthday || '');
        
        // 设置性别
        $("input[name='user_gender'][value='" + (user.user_gender || 0) + "']").prop('checked', true);
        
        // 设置头像
        if (user.user_profile_picture_src) {
            $("#header_image").attr("src", API.baseUrl + "/res/images/item/userProfilePicture/" + user.user_profile_picture_src);
            $("#user_profile_picture_src_value").val(user.user_profile_picture_src);
        }
        
        // 填充地址信息
        if (data.addressList) {
            this.populateAddressSelects(data);
        }
        
        // 更新页面显示的用户名
        $("#username_display").text(user.user_name || '');

        // 更新侧边栏头像
        if (user.user_profile_picture_src) {
            $("#sidebar_avatar").attr("src", API.baseUrl + "/res/images/item/userProfilePicture/" + user.user_profile_picture_src);
        }
    },
    
    // 填充地址选择框
    populateAddressSelects: function(data) {
        // 填充省份
        var provinceSelect = $("#select_user_address_province");
        provinceSelect.empty();
        if (data.addressList) {
            data.addressList.forEach(function(address) {
                var selected = data.addressId === address.address_areaId ? 'selected' : '';
                provinceSelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 填充城市
        var citySelect = $("#select_user_address_city");
        citySelect.empty();
        if (data.cityList) {
            data.cityList.forEach(function(address) {
                var selected = data.cityAddressId === address.address_areaId ? 'selected' : '';
                citySelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 填充区县
        var districtSelect = $("#select_user_address_district");
        districtSelect.empty();
        if (data.districtList) {
            data.districtList.forEach(function(address) {
                var selected = data.districtAddressId === address.address_areaId ? 'selected' : '';
                districtSelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 刷新选择器
        $('.selectpicker').selectpicker('refresh');
    },
    
    // 加载城市列表
    loadCityList: function(provinceId) {
        var self = this;
        
        API.getCityList(provinceId, function(data) {
            var citySelect = $("#select_user_address_city");
            var districtSelect = $("#select_user_address_district");
            
            citySelect.empty();
            districtSelect.empty();
            
            data.forEach(function(address) {
                citySelect.append('<option value="' + address.address_areaId + '">' + address.address_name + '</option>');
            });
            
            $('.selectpicker').selectpicker('refresh');
        }, function(message) {
            console.error('获取城市列表失败：', message);
        });
    },
    
    // 加载区县列表
    loadDistrictList: function(cityId) {
        var self = this;
        
        API.getDistrictList(cityId, function(data) {
            var districtSelect = $("#select_user_address_district");
            districtSelect.empty();
            
            data.forEach(function(address) {
                districtSelect.append('<option value="' + address.address_areaId + '">' + address.address_name + '</option>');
            });
            
            $('.selectpicker').selectpicker('refresh');
        }, function(message) {
            console.error('获取区县列表失败：', message);
        });
    },
    
    // 处理更新用户信息
    handleUpdateUserInfo: function() {
        var self = this;
        
        // 验证表单
        if (!this.validateForm()) {
            return;
        }
        
        // 获取表单数据
        var formData = {
            user_nickname: $("#user_nickname").val(),
            user_realname: $("#user_realname").val(),
            user_gender: $("input[name='user_gender']:checked").val(),
            user_birthday: $("#user_birthday").val(),
            user_address_province: $("#select_user_address_province").val(),
            user_address_city: $("#select_user_address_city").val(),
            user_address_district: $("#select_user_address_district").val(),
            user_address_detail: $("#textarea_details_address").val(),
            user_profile_picture_src: $("#user_profile_picture_src_value").val()
        };
        
        // 显示提交中状态
        $(".register_btn").val("提交中...");
        
        API.updateUserInfo(formData, function(data) {
            $(".register_btn").val("提交");
            self.showSuccess("个人信息修改成功！");
        }, function(message) {
            $(".register_btn").val("提交");
            self.showError('修改个人信息失败：' + message);
        });
    },
    
    // 处理头像上传
    handleAvatarUpload: function(input) {
        var self = this;
        
        if (input.files && input.files[0]) {
            // 验证文件类型和大小
            var file = input.files[0];
            if (!this.validateImageFile(file)) {
                return;
            }
            
            // 创建FormData对象
            var formData = new FormData();
            formData.append('file', file);
            
            // 显示上传中状态
            $("#upload_avatar_btn").text("上传中...");
            
            API.uploadAvatar(formData, function(data) {
                $("#upload_avatar_btn").text("上传头像");
                
                // 更新头像预览
                $("#header_image").attr("src", API.baseUrl + "/res/images/item/userProfilePicture/" + data.fileName);
                $("#user_profile_picture_src_value").val(data.fileName);
                
                self.showSuccess("头像上传成功！");
            }, function(message) {
                $("#upload_avatar_btn").text("上传头像");
                self.showError('头像上传失败：' + message);
            });
        }
    },
    
    // 验证图片文件
    validateImageFile: function(file) {
        // 验证文件类型
        var allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (allowedTypes.indexOf(file.type) === -1) {
            this.showError('请选择JPEG、PNG或GIF格式的图片！');
            return false;
        }
        
        // 验证文件大小（最大2MB）
        var maxSize = 2 * 1024 * 1024; // 2MB
        if (file.size > maxSize) {
            this.showError('图片大小不能超过2MB！');
            return false;
        }
        
        return true;
    },
    
    // 表单验证
    validateForm: function() {
        var nickname = $.trim($("#user_nickname").val());
        
        if (nickname === "") {
            this.showError("请输入昵称！");
            return false;
        }
        
        return true;
    },
    
    // 显示错误信息
    showError: function(message) {
        alert(message);
    },
    
    // 显示成功信息
    showSuccess: function(message) {
        alert(message);
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    UserInfoAPI.init();
});
