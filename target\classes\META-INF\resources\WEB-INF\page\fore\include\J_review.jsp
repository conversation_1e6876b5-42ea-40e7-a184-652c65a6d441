<%@ page contentType="text/html;charset=UTF-8" %>
<style rel="stylesheet">
    .J_reviews {
        width: 100%;
        display: none;
        margin-bottom: 10px;
    }

    .reviews_info {
        width: 100%;
        padding: 16px 7px;
        border-bottom: 1px solid #e3e3e3;
        font-family: tahoma, arial, \5FAE\8F6F\96C5\9ED1, sans-serif;
        font-size: 12px;
        min-height: 68px;
        box-sizing: border-box;
    }

    .reviews_main {
        display: inline-block;
    }

    .reviews_content {
        width: 494px;
        padding-right: 30px;
        line-height: 19px;
        overflow: hidden;
        word-wrap: break-word;
        word-break: break-all;
        color: #333333;
    }

    .reviews_date {
        clear: both;
        color: #cccccc;
    }

    .reviews_author {
        position: relative;
        right: 30px;
        float: right;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        min-width: 100px;
    }

    .reviews_loading {
        text-align: center;
        padding: 20px;
        color: #999;
    }

    .reviews_empty {
        text-align: center;
        padding: 40px;
        color: #999;
    }

    .reviews_pagination {
        text-align: center;
        padding: 20px 0;
    }

    .reviews_pagination a {
        display: inline-block;
        padding: 5px 10px;
        margin: 0 2px;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
    }

    .reviews_pagination a:hover {
        background-color: #f5f5f5;
    }

    .reviews_pagination a.current {
        background-color: #ff6600;
        color: white;
        border-color: #ff6600;
    }

    .reviews_pagination a.disabled {
        color: #ccc;
        cursor: not-allowed;
    }
</style>

<div class="J_reviews">
    <!-- 加载中提示 -->
    <div id="reviewsLoading" class="reviews_loading">
        正在加载评价信息...
    </div>

    <!-- 评价列表容器 -->
    <div id="reviewsContainer" class="J_reviews_main" style="display: none;">
        <!-- 评价列表将通过JavaScript动态加载 -->
    </div>

    <!-- 无评价提示 -->
    <div id="reviewsEmpty" class="reviews_empty" style="display: none;">
        暂无评价信息
    </div>

    <!-- 分页容器 -->
    <div id="reviewsPagination" class="reviews_pagination" style="display: none;">
        <!-- 分页将通过JavaScript动态加载 -->
    </div>
</div>

<script>
// 评价组件对象
var ReviewComponent = {
    // 当前参数
    currentParams: {
        productId: null,
        index: 0,
        count: 5
    },

    // 当前数据
    currentData: null,

    // 初始化
    init: function(productId) {
        this.currentParams.productId = productId;
        this.loadReviews();
    },

    // 加载评价数据
    loadReviews: function() {
        var self = this;

        if (!self.currentParams.productId) {
            console.error('产品ID不能为空');
            return;
        }

        // 显示加载中
        $('#reviewsLoading').show();
        $('#reviewsContainer').hide();
        $('#reviewsEmpty').hide();
        $('#reviewsPagination').hide();

        $.ajax({
            url: '/tmall/api/review/' + self.currentParams.productId,
            type: 'GET',
            data: {
                index: self.currentParams.index,
                count: self.currentParams.count
            },
            dataType: 'json',
            success: function(response) {
                $('#reviewsLoading').hide();

                if (response && response.reviewList) {
                    self.currentData = response;
                    self.renderReviews(response.reviewList);
                    self.renderPagination(response.pageUtil);
                } else {
                    self.showEmptyMessage();
                }
            },
            error: function(xhr, status, error) {
                $('#reviewsLoading').hide();
                console.error('加载评价失败:', error);
                self.showEmptyMessage();
            }
        });
    },

    // 渲染评价列表
    renderReviews: function(reviewList) {
        if (!reviewList || reviewList.length === 0) {
            this.showEmptyMessage();
            return;
        }

        var html = '';
        for (var i = 0; i < reviewList.length; i++) {
            var review = reviewList[i];
            html += '<div class="reviews_info">';
            html += '    <div class="reviews_main">';
            html += '        <div class="reviews_content">';
            html += '            <p>' + (review.review_content || '') + '</p>';
            html += '        </div>';
            html += '        <div class="reviews_date">';
            html += '            <span>' + (review.review_createDate || '') + '</span>';
            html += '        </div>';
            html += '    </div>';
            html += '    <div class="reviews_author">' + (review.review_user ? (review.review_user.user_nickname || '匿名用户') : '匿名用户') + '</div>';
            html += '</div>';
        }

        $('#reviewsContainer').html(html).show();
    },

    // 渲染分页
    renderPagination: function(pageUtil) {
        if (!pageUtil || pageUtil.totalPage <= 1) {
            $('#reviewsPagination').hide();
            return;
        }

        var html = '';

        // 上一页
        if (pageUtil.index > 0) {
            html += '<a href="javascript:void(0)" onclick="ReviewComponent.goToPage(' + (pageUtil.index - 1) + ')">上一页</a>';
        } else {
            html += '<a href="javascript:void(0)" class="disabled">上一页</a>';
        }

        // 页码
        var startPage = Math.max(0, pageUtil.index - 2);
        var endPage = Math.min(pageUtil.totalPage - 1, pageUtil.index + 2);

        for (var i = startPage; i <= endPage; i++) {
            if (i === pageUtil.index) {
                html += '<a href="javascript:void(0)" class="current">' + (i + 1) + '</a>';
            } else {
                html += '<a href="javascript:void(0)" onclick="ReviewComponent.goToPage(' + i + ')">' + (i + 1) + '</a>';
            }
        }

        // 下一页
        if (pageUtil.index < pageUtil.totalPage - 1) {
            html += '<a href="javascript:void(0)" onclick="ReviewComponent.goToPage(' + (pageUtil.index + 1) + ')">下一页</a>';
        } else {
            html += '<a href="javascript:void(0)" class="disabled">下一页</a>';
        }

        $('#reviewsPagination').html(html).show();
    },

    // 显示空消息
    showEmptyMessage: function() {
        $('#reviewsEmpty').show();
    },

    // 跳转到指定页
    goToPage: function(index) {
        if (index < 0 || (this.currentData && this.currentData.pageUtil && index >= this.currentData.pageUtil.totalPage)) {
            return;
        }
        this.currentParams.index = index;
        this.loadReviews();
    }
};
</script>