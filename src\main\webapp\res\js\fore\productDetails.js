/**
 * 商品详情页面数据渲染JavaScript
 */
class ProductDetailsRenderer {
    constructor() {
        this.productId = null;
        this.productData = null;
        this.contextPath = '';
    }

    /**
     * 初始化
     */
    init(productId, contextPath) {
        console.log('ProductDetailsRenderer.init called with:', productId, contextPath);
        this.productId = productId;
        this.contextPath = contextPath;
        this.loadProductDetails();
        this.initEventHandlers();
    }

    /**
     * 初始化事件处理器
     */
    initEventHandlers() {
        // 初始化选项卡
        $(".J_GoodsDetails").addClass("tab-selected");
        $(".context_img_li").eq(0).addClass("context_img_li_hover");

        // 搜索框验证
        $('form').submit(function () {
            if ($(this).find("input[name='product_name']").val() === "") {
                alert("请输入关键字！");
                return false;
            }
        });

        // 移入预览图片列表时
        $(".context_img_li").mouseenter(function () {
            const img = $(this).children("img");
            $(".context_img_main").attr("src", img.attr("src"));
            $(".context_img_ks").children("img").attr("src", img.attr("src"));
            $(".context_img_li").removeClass("context_img_li_hover");
            $(this).addClass("context_img_li_hover");
        });

        // 产品数量框验证
        $(".amount_value_up").click(function () {
            let number = parseInt($(".context_buymember").val());
            number++;
            $(".context_buymember").val(number);
        });

        $(".amount_value-down").click(function () {
            let number = parseInt($(".context_buymember").val());
            if (number > 1) {
                number--;
                $(".context_buymember").val(number);
            }
        });

        $(".context_buymember").on("input", function () {
            if ($(this).val() === "") {
                $(this).val(1);
            }
            if (parseInt($("#stock").val()) < parseInt($(this).val())) {
                $(".context_buyNow").addClass("context_notBuy").attr("disabled", "disabled");
                $(".context_addBuyCar").addClass("context_notCar").attr("disabled", "disabled");
            } else {
                $(".context_buyNow").removeClass("context_notBuy").attr("disabled", null);
                $(".context_addBuyCar").removeClass("context_notCar").attr("disabled", null);
            }
        });

        // 点击猜你喜欢翻页按钮时
        const ul = $(".context_ul_goodsList").children("ul");
        $(".ul_trigger_up").click(function () {
            const ulTop = parseInt(ul.css("top"));
            const fTop = ulTop + 480;
            if (fTop > 0) {
                ul.animate({
                    top: ulTop + 40
                }, 100, function () {
                    ul.animate({
                        top: 0
                    }, 100);
                });
            } else {
                ul.animate({
                    top: fTop
                }, 200);
            }
        });

        $(".ul_trigger_down").click(function () {
            const ulTop = parseInt(ul.css("top"));
            const fTop = ulTop - 480;
            if (ul.height() < 2880) {
                this.getRecommendProducts();
            }
            if (fTop < -2400) {
                ul.animate({
                    top: ulTop - 40
                }, 100, function () {
                    ul.animate({
                        top: -2400
                    }, 100);
                });
            } else {
                ul.animate({
                    top: fTop
                }, 200);
            }
        }.bind(this));

        // 放大镜逻辑
        $(".context_img_main").mouseenter(function () {
            $(".context_img_winSelector").show();
            $(".context_img_ks").show().children("img").attr("src", $(this).attr("src"));
        });

        $(".context_img_winSelector").mouseleave(function () {
            $(".context_img_winSelector").hide();
            $(".context_img_ks").hide();
        });

        $(".context_img_main,.context_img_winSelector").mousemove(function (e) {
            this.selectorMousemove(e);
        }.bind(this));
    }

    /**
     * 放大镜鼠标移动处理
     */
    selectorMousemove(e) {
        const $img = $(".context_img_main");
        const $selector = $(".context_img_winSelector");
        const $imgWidth = $img.width();
        const $imgHeight = $img.height();
        const $selectorWidth = $selector.width();
        const $selectorHeight = $selector.height();
        
        // 扫描器的定位
        // 获取光标正中位置
        let x = e.pageX - $img.offset().left - $selectorWidth / 2;
        let y = e.pageY - $img.offset().top - $selectorHeight / 2;
        x = x < 0 ? 0 : x;
        y = y < 0 ? 0 : y;
        x = x > $imgWidth - $selectorWidth ? $imgWidth - $selectorWidth : x;
        y = y > $imgHeight - $selectorHeight ? $imgHeight - $selectorHeight : y;
        $selector.css({left: x, top: y});
        const naturalNumber = $('.context_img_ks').width() / $selectorWidth;
        // 1.917为转换系数
        $('.context_img_ks>img').css({
            left: -x * 1.917,
            top: -y * 1.917
        });
    }

    /**
     * 获取推荐商品
     */
    getRecommendProducts() {
        const self = this;
        const categoryId = $("#tid").val();
        const params = {
            guessNumber: $("#guessNumber").val()
        };
        
        API.getRecommendProducts(categoryId, params, function(data) {
            if (data && data.loveProductList) {
                $("#guessNumber").val(data.guessNumber);
                for (let i = 0; i < data.loveProductList.length; i++) {
                    const product = data.loveProductList[i];
                    const src = product.singleProductImageList[0].productImage_src;
                    const product_id = product.product_id;
                    const product_sale_price = product.product_sale_price;
                    $(".context_ul_goodsList").children("ul").append(
                        `<li class='context_ul_main'>
                            <div class='context_ul_img'>
                                <a href='${self.contextPath}/product/${product_id}'>
                                    <img src='${self.contextPath}/res/images/item/productSinglePicture/${src}'/>
                                </a>
                                <p>¥${product_sale_price}.00</p>
                            </div>
                        </li>`
                    );
                }
            }
        }, function(message) {
            console.error("获取推荐商品失败:", message);
        });
    }

    /**
     * 切换详情页选项卡
     */
    getDetailsPage(obj, className) {
        $(".J_TabBarBox").find("li").removeClass("tab-selected");
        $(obj).parent("li").addClass("tab-selected");
        $(".J_choose").children("div").hide();
        $("." + className).show();
    }

    /**
     * 加载商品详情数据
     */
    loadProductDetails() {
        const self = this;
        
        console.log('Loading product details for ID:', this.productId);
        
        API.getProductDetail(this.productId, function(data) {
            console.log('Product details response:', data);
            self.productData = data;
            self.renderProductDetails();
        }, function(message) {
            console.error("获取商品详情失败:", message);
            self.showError("商品信息加载失败: " + message);
        });
    }

    /**
     * 渲染商品详情
     */
    renderProductDetails() {
        console.log('开始渲染商品详情，数据：', this.productData);
        this.renderBasicInfo();
        this.renderImages();
        this.renderPriceInfo();
        this.renderSalesInfo();
        this.renderProperties();
        this.renderRecommendedProducts();
        this.renderDetailImages();
        this.renderCategoryNav();
        console.log('商品详情渲染完成');
    }

    /**
     * 渲染基本信息
     */
    renderBasicInfo() {
        const product = this.productData.product;
        console.log('渲染基本信息，商品数据：', product);

        // 设置商品名称和标题
        $('.context_info_name').text(product.product_name);
        $('.context_info_title').text(product.product_title);
        console.log('设置商品名称：', product.product_name);
        
        // 设置页面标题
        document.title = product.product_name + '-趣味商城';
        
        // 设置店铺名称
        $('.shopNameHeader').text('海涛' + product.product_category.category_name + '官方旗舰店');
        
        // 设置分类ID
        $('#tid').val(product.product_category.category_id);
        
        // 设置店铺背景图
        $('.shopImg img').attr('src', this.contextPath + '/res/images/item/categoryPicture/' + product.product_category.category_image_src);
    }

    /**
     * 渲染商品图片
     */
    renderImages() {
        const product = this.productData.product;
        
        if (product.singleProductImageList && product.singleProductImageList.length > 0) {
            const firstImage = product.singleProductImageList[0];
            const imagePath = this.contextPath + '/res/images/item/productSinglePicture/' + firstImage.productImage_src;
            
            // 设置主图
            $('.context_img_main').attr('src', imagePath);
            $('.context_img_ks img').attr('src', imagePath);
            
            // 渲染图片列表
            const imageListHtml = product.singleProductImageList.map(img => {
                return `<li class="context_img_li">
                    <img src="${this.contextPath}/res/images/item/productSinglePicture/${img.productImage_src}"/>
                </li>`;
            }).join('');
            
            $('.context_img_ul').html(imageListHtml);
            
            // 设置第一张图片为选中状态
            $('.context_img_li').eq(0).addClass('context_img_li_hover');
        }
    }

    /**
     * 渲染价格信息
     */
    renderPriceInfo() {
        const product = this.productData.product;
        
        // 设置原价
        $('.context_price_panel dd span').text(product.product_price + '0');
        
        // 设置促销价
        $('.context_promotePrice_panel dd span').text(product.product_sale_price + '0');
        
        // 设置天猫积分
        const points = Math.floor(product.product_sale_price / 10);
        $('.tmall_points span').text(points);
    }

    /**
     * 渲染销售信息
     */
    renderSalesInfo() {
        const product = this.productData.product;
        
        // 设置销量
        const saleCount = product.product_sale_count || 0;
        $('.context_other_panel li:first span').text(saleCount);
        
        // 设置评价数
        $('.context_other_panel li:nth-child(2) span').text(product.product_review_count);
        
        // 更新评价标签页的数量
        $('.J_GoodsReviews a span').text(product.product_review_count);
    }

    /**
     * 渲染商品属性
     */
    renderProperties() {
        const propertyList = this.productData.propertyList;

        if (propertyList && propertyList.length > 0) {
            const propertiesHtml = propertyList.map(property => {
                if (property.propertyValueList && property.propertyValueList.length > 0 &&
                    property.propertyValueList[0].propertyValue_value) {
                    const value = property.propertyValueList[0].propertyValue_value;
                    const name = property.property_name;
                    return `<li title="${this.escapeHtml(value)}">
                        ${this.escapeHtml(name)}：${this.escapeHtml(value)}
                    </li>`;
                }
                return '';
            }).filter(html => html !== '').join('');

            $('.J_details_list_body').html(propertiesHtml);
            $('.J_details_list_header span').text(this.productData.product.product_name);
        }
    }

    /**
     * HTML转义函数
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 渲染推荐商品
     */
    renderRecommendedProducts() {
        const loveProductList = this.productData.loveProductList;
        
        if (loveProductList && loveProductList.length > 0) {
            const recommendHtml = loveProductList.map(product => {
                const imageUrl = product.singleProductImageList && product.singleProductImageList.length > 0 
                    ? this.contextPath + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src
                    : '';
                
                return `<li class="context_ul_main">
                    <div class="context_ul_img">
                        <a href="${this.contextPath}/product/${product.product_id}">
                            <img src="${imageUrl}">
                        </a>
                        <p>¥${product.product_sale_price}0</p>
                    </div>
                </li>`;
            }).join('');
            
            $('.context_ul_goodsList ul').html(recommendHtml);
            $('#guessNumber').val(this.productData.guessNumber);
        }
    }

    /**
     * 渲染详情图片
     */
    renderDetailImages() {
        const product = this.productData.product;
        
        if (product.detailProductImageList && product.detailProductImageList.length > 0) {
            const detailImagesHtml = product.detailProductImageList.map(image => {
                return `<img src="${this.contextPath}/res/images/item/productDetailsPicture/${image.productImage_src}"/>`;
            }).join('');
            
            $('.J_img').html(detailImagesHtml);
        }
    }

    /**
     * 渲染分类导航
     */
    renderCategoryNav() {
        const categoryList = this.productData.categoryList;
        
        if (categoryList && categoryList.length > 0) {
            const categoryHtml = categoryList.map(category => {
                return `<li>
                    <a href="${this.contextPath}/product?category_id=${category.category_id}">${category.category_name}</a>
                </li>`;
            }).join('');
            
            $('.shopSearchHeader ul').html(categoryHtml);
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        alert(message);
    }
}

// 创建全局实例
window.ProductDetailsRenderer = new ProductDetailsRenderer();
