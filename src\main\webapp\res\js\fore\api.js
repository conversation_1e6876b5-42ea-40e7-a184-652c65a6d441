/**
 * API接口工具
 * 用于前端调用后端API
 */
var API = {
    // 基础URL
    baseUrl: '',
    
    // API路径
    ENDPOINTS: {
        // 用户认证相关
        LOGIN: '/api/auth/login',
        LOGOUT: '/api/auth/logout',
        VERIFY_CODE: '/api/auth/code',
        REGISTER: '/api/auth/register',
        
        // 用户信息相关
        USER_INFO: '/api/users/info',
        UPDATE_USER_INFO: '/api/users',
        UPLOAD_AVATAR: '/api/users/avatar',
        
        // 地址相关
        ADDRESS_LIST: '/api/addresses',
        ADDRESS_BY_PARENT: '/api/addresses/', // 后面需要拼接父ID
        
        // 商品相关
        PRODUCT_DETAIL: '/api/products/', // 后面需要拼接商品ID
        PRODUCT_REVIEWS: '/api/products/', // 后面需要拼接商品ID + /reviews
        PRODUCT_PROPERTIES: '/api/products/', // 后面需要拼接商品ID + /properties
        PRODUCT_RECOMMENDATIONS: '/api/products/recommendations'
    },
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.initInterceptors();
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 初始化拦截器
    initInterceptors: function() {
        var self = this;
        
        // AJAX全局设置
        $.ajaxSetup({
            beforeSend: function(xhr) {
                // 添加Token认证
                var token = self.getToken();
                if (token) {
                    xhr.setRequestHeader('Authorization', 'Bearer ' + token);
                }
            },
            complete: function(xhr) {
                // 处理401未授权错误
                if (xhr.status === 401) {
                    self.removeToken();
                    // 如果不是登录页面，则跳转到登录页
                    if (!window.location.pathname.includes('/login')) {
                        window.location.href = self.baseUrl + '/login';
                    }
                }
            }
        });
    },
    
    // 获取JWT Token
    getToken: function() {
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    },

    // 设置JWT Token
    setToken: function(token) {
        localStorage.setItem('token', token);
    },

    // 移除JWT Token
    removeToken: function() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
    },
    
    // 通用请求函数
    request: function(options) {
        var self = this;
        
        // 构建完整URL
        var url = this.baseUrl + options.url;
        
        // 默认设置
        var settings = {
            url: url,
            type: options.method || 'GET',
            data: options.data || {},
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    if (options.success) {
                        options.success(response.data);
                    }
                } else {
                    if (options.error) {
                        options.error(response.message, response.code);
                    } else {
                        console.error('请求失败：', response.message);
                    }
                }
            },
            error: function(xhr, status, error) {
                if (options.error) {
                    options.error(xhr.responseJSON?.message || error, xhr.status);
                } else {
                    console.error('请求失败：', error);
                }
            }
        };
        
        // 合并自定义设置
        if (options.contentType) {
            settings.contentType = options.contentType;
        }
        
        if (options.processData !== undefined) {
            settings.processData = options.processData;
        }
        
        // 发送请求
        return $.ajax(settings);
    },
    
    // ===== 用户认证相关API =====
    
    /**
     * 用户登录
     * @param {Object} data - 登录信息 {username, password, verifyCode}
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    login: function(data, success, error) {
        return this.request({
            url: this.ENDPOINTS.LOGIN,
            method: 'POST',
            data: data,
            success: function(data) {
                // 保存Token
                if (data && data.token) {
                    API.setToken(data.token);
                }
                if (success) success(data);
            },
            error: error
        });
    },
    
    /**
     * 用户退出登录
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    logout: function(success, error) {
        var self = this;
        return this.request({
            url: this.ENDPOINTS.LOGOUT,
            method: 'POST',
            success: function(data) {
                // 清除Token
                self.removeToken();
                if (success) success(data);
            },
            error: function(msg, code) {
                // 无论是否成功都清除Token
                self.removeToken();
                if (error) error(msg, code);
            }
        });
    },
    
    /**
     * 获取验证码
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getVerifyCode: function(success, error) {
        return this.request({
            url: this.ENDPOINTS.VERIFY_CODE,
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    /**
     * 用户注册
     * @param {Object} data - 注册信息
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    register: function(data, success, error) {
        return this.request({
            url: this.ENDPOINTS.REGISTER,
            method: 'POST',
            data: data,
            success: success,
            error: error
        });
    },
    
    // ===== 用户信息相关API =====
    
    /**
     * 获取用户信息
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getUserInfo: function(success, error) {
        return this.request({
            url: this.ENDPOINTS.USER_INFO,
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    /**
     * 更新用户信息
     * @param {Object} data - 用户信息
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    updateUserInfo: function(data, success, error) {
        return this.request({
            url: this.ENDPOINTS.UPDATE_USER_INFO,
            method: 'PUT',
            data: data,
            success: success,
            error: error
        });
    },
    
    /**
     * 上传用户头像
     * @param {FormData} formData - 包含文件的FormData
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    uploadAvatar: function(formData, success, error) {
        return this.request({
            url: this.ENDPOINTS.UPLOAD_AVATAR,
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: success,
            error: error
        });
    },
    
    // ===== 地址相关API =====
    
    /**
     * 获取地址列表
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getAddressList: function(success, error) {
        return this.request({
            url: this.ENDPOINTS.ADDRESS_LIST,
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    /**
     * 获取子地址列表
     * @param {String} parentId - 父地址ID
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getChildAddressList: function(parentId, success, error) {
        return this.request({
            url: this.ENDPOINTS.ADDRESS_BY_PARENT + parentId,
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    // ===== 商品相关API =====
    
    /**
     * 获取商品详情
     * @param {Number} productId - 商品ID
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getProductDetail: function(productId, success, error) {
        return this.request({
            url: this.ENDPOINTS.PRODUCT_DETAIL + productId,
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    /**
     * 获取商品评论
     * @param {Number} productId - 商品ID
     * @param {Object} params - 查询参数 {index, count}
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getProductReviews: function(productId, params, success, error) {
        return this.request({
            url: this.ENDPOINTS.PRODUCT_REVIEWS + productId + '/reviews',
            method: 'GET',
            data: params,
            success: success,
            error: error
        });
    },
    
    /**
     * 获取商品属性
     * @param {Number} productId - 商品ID
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getProductProperties: function(productId, success, error) {
        return this.request({
            url: this.ENDPOINTS.PRODUCT_PROPERTIES + productId + '/properties',
            method: 'GET',
            success: success,
            error: error
        });
    },
    
    /**
     * 获取推荐商品
     * @param {Object} params - 查询参数 {categoryId, guessNumber}
     * @param {Function} success - 成功回调
     * @param {Function} error - 错误回调
     */
    getRecommendedProducts: function(params, success, error) {
        return this.request({
            url: this.ENDPOINTS.PRODUCT_RECOMMENDATIONS,
            method: 'GET',
            data: params,
            success: success,
            error: error
        });
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    API.init();
});