# API接口规范化计划

## 现状分析
经过对项目代码的分析，发现以下几个问题：
1. API路径格式不统一，有些使用`api/xxx`格式，有些直接使用功能名称
2. 部分接口返回格式不统一，有些使用`Result<T>`，有些直接返回字符串
3. 部分接口没有使用RESTful风格的API设计
4. 部分接口缺少错误处理和统一的响应格式

## 规范标准

### 1. API路径格式
- 所有API接口统一使用`/api/`前缀
- 接口路径采用资源名称的复数形式，如`/api/products`，`/api/users`
- 使用版本控制，如`/api/v1/products`（可选）
- 使用kebab-case（短横线）命名方式，如`/api/product-categories`

### 2. HTTP方法使用规范
- GET：获取资源
- POST：创建资源
- PUT：更新资源（全量更新）
- PATCH：部分更新资源（部分更新）
- DELETE：删除资源

### 3. 统一响应格式
所有API接口统一使用`Result<T>`类作为响应格式，包含：
- code：状态码，200表示成功，其他表示失败
- message：响应消息
- data：响应数据
- timestamp：时间戳

### 4. 错误处理
- 使用HTTP状态码表示请求处理的状态
- 使用Result中的code和message提供更详细的错误信息
- 对常见错误进行统一处理，如参数校验错误、权限错误等

## 需要规范化的接口

### 1. 用户认证相关接口
- ForeLoginController
  - 登录接口：`/api/auth/login`（已规范）
  - 登出接口：`/api/auth/logout`（已规范）
  - 获取验证码：`/api/auth/code`（已规范）

### 2. 用户信息相关接口
- ForeUserController
  - 获取用户信息：`/api/users/info`（需规范）
  - 更新用户信息：`/api/users/update`（需规范）
  - 上传用户头像：`/api/users/avatar`（需规范）

### 3. 商品相关接口
- ForeProductDetailsController
  - 获取商品详情：`/api/products/{pid}`（需规范）
  - 获取商品评论：`/api/products/{pid}/reviews`（需规范）
  - 获取商品属性：`/api/products/{pid}/properties`（需规范）
  - 获取推荐商品：`/api/products/recommendations`（需规范）

### 4. 订单相关接口
- ForeOrderController
  - 获取订单列表：`/api/orders`（需规范）
  - 创建订单：`/api/orders`（需规范）
  - 更新订单状态：`/api/orders/{order_code}/status`（需规范）
  - 获取订单详情：`/api/orders/{order_code}`（需规范）

### 5. 注册相关接口
- ForeRegisterController
  - 用户注册：`/api/auth/register`（需规范）
  - 获取地址信息：`/api/addresses`（需规范）

## 实施步骤
1. 先规范化基础接口，如用户认证、用户信息等
2. 再规范化业务接口，如商品、订单等
3. 最后规范化辅助接口，如地址、评论等
4. 更新前端调用代码，确保与新API接口兼容
5. 编写API接口文档，方便前端开发人员使用 