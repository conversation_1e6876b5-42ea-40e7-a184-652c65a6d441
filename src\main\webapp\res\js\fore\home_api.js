/**
 * 首页API数据加载和渲染模块
 */
var HomeAPI = {
    
    // API基础路径
    baseUrl: '',
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.loadHomeData();
        this.bindEvents();
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 获取JWT Token
    getToken: function() {
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    },

    // 设置JWT Token
    setToken: function(token) {
        localStorage.setItem('token', token);
    },

    // 移除JWT Token
    removeToken: function() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
    },
    
    // 绑定事件
    bindEvents: function() {
        var self = this;
        
        // 绑定轮播图点击事件
        $(document).on('click', '.banner_main > a', function() {
            var productId = $(this).attr('product-id');
            if (productId) {
                window.location.href = API.baseUrl + '/product/' + productId;
            }
        });
        
        // 绑定分类导航事件
        $('.banner_nav > li').hover(
            function() {
                var categoryId = $(this).data('toggle');
                self.loadCategoryProducts(categoryId);
                $(this).children('.banner_div').show();
            },
            function() {
                $(this).children('.banner_div').hide();
            }
        );
    },
    
    // 加载首页数据
    loadHomeData: function() {
        var self = this;
        
        API.getHomeData(function(data) {
            self.renderHomeData(data);
        }, function(message) {
            console.error('获取首页数据失败：', message);
            self.showError('获取首页数据失败：' + message);
        });
    },
    
    // 渲染首页数据
    renderHomeData: function(data) {
        // 通知导航栏更新用户信息（如果导航栏管理器存在）
        if (window.NavigatorManager && typeof window.NavigatorManager.updateUserInfo === 'function') {
            window.NavigatorManager.updateUserInfo(data.user);
        } else {
            // 如果导航栏管理器不存在，直接渲染用户信息
            this.renderUserInfo(data.user);
        }

        // 渲染分类列表
        this.renderCategoryList(data.categoryList);

        // 渲染促销产品
        this.renderSpecialProducts(data.specialProductList);

        // 渲染分类下的产品
        this.renderCategoryProducts(data.categoryList);
    },
    
    // 渲染用户信息
    renderUserInfo: function(user) {
        var loginContainer = $('#container_login');
        if (user) {
            // 用户已登录
            loginContainer.html(
                '<em>Hi，</em>' +
                '<a href="' + API.baseUrl + '/userDetails" class="userName" target="_blank">' + user.user_name + '</a>' +
                '<a href="javascript:void(0)" onclick="API.logout(function() { window.location.reload(); })">退出</a>'
            );
        } else {
            // 用户未登录
            loginContainer.html(
                '<em>喵，欢迎来天猫</em>' +
                '<a href="' + API.baseUrl + '/login">请登录</a>' +
                '<a href="' + API.baseUrl + '/register">免费注册</a>'
            );
        }
    },
    
    // 渲染分类列表
    renderCategoryList: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;
        
        // 渲染搜索区域的分类链接
        var searchCategoryHtml = '';
        for (var i = 0; i < categoryList.length && i < 9; i++) {
            var category = categoryList[i];
            var categoryName = category.category_name;
            if (categoryName.indexOf(' /') > -1) {
                categoryName = categoryName.substring(0, categoryName.indexOf(' /'));
            }
            var colorStyle = i % 2 !== 0 ? ' style="color: #FF0036"' : '';
            searchCategoryHtml += '<li><a href="' + API.baseUrl + '/product?category_id=' + 
                                 category.category_id + '"' + colorStyle + '>' + categoryName + '</a></li>';
        }
        $('.mallSearch ul').html(searchCategoryHtml);
        
        // 渲染导航区域的分类
        var navCategoryHtml = '';
        for (var j = 0; j < categoryList.length; j++) {
            var cat = categoryList[j];
            navCategoryHtml += '<li data-toggle="' + cat.category_id + '" data-status="">' +
                              '<img src="' + API.baseUrl + '/res/images/fore/WebsiteImage/small/' + cat.category_id + '.png">' +
                              '<a href="' + API.baseUrl + '/product?category_id=' + cat.category_id + '">' + cat.category_name + '</a>' +
                              '<div class="banner_div" name="' + cat.category_name + '"></div>' +
                              '</li>';
        }
        $('.banner_nav').html(navCategoryHtml);
    },
    
    // 渲染促销产品
    renderSpecialProducts: function(specialProductList) {
        if (!specialProductList || specialProductList.length === 0) return;
        
        var bannerHtml = '';
        for (var i = 0; i < specialProductList.length; i++) {
            var product = specialProductList[i];
            var displayStyle = i === 0 ? ' style="display: block;"' : '';
            bannerHtml += '<img src="' + API.baseUrl + '/res/images/fore/WebsiteImage/banner/' + 
                         product.product_id + '.jpg" name="' + product.product_id + 
                         '" id="banner' + (i + 1) + '"' + displayStyle + ' />';
        }
        $('.banner').html(bannerHtml);
        
        // 初始化轮播图功能
        this.initBannerSlider();
    },
    
    // 渲染分类下的产品
    renderCategoryProducts: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;

        var goodsHtml = '';
        for (var i = 0; i < categoryList.length; i++) {
            var category = categoryList[i];
            if (category.productList && category.productList.length > 0) {
                goodsHtml += '<div class="banner_goods_type">' +
                            '<div class="banner_goods_title">' +
                            '<span></span>' +
                            '<p>' + category.category_name + '</p>' +
                            '</div>' +
                            '<a href="' + API.baseUrl + '/product?category_id=' + category.category_id + '">' +
                            '<img class="banner_goods_show" src="' + API.baseUrl + '/res/images/fore/WebsiteImage/show/' + category.category_id + '.jpg">' +
                            '</a>' +
                            '<div class="banner_goods_items">';

                for (var j = 0; j < category.productList.length && j < 8; j++) {
                    var product = category.productList[j];
                    var imageUrl = '';
                    if (product.singleProductImageList && product.singleProductImageList.length > 0) {
                        imageUrl = API.baseUrl + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src;
                    }

                    goodsHtml += '<div class="banner_goods_item">' +
                                '<a href="' + API.baseUrl + '/product/' + product.product_id + '" class="goods_link"></a>' +
                                '<img src="' + imageUrl + '">' +
                                '<a href="' + API.baseUrl + '/product/' + product.product_id + '" class="goods_name">' + product.product_name + '</a>' +
                                '<span class="goods_price">￥' + product.product_sale_price + '</span>' +
                                '</div>';
                }

                goodsHtml += '</div></div>';
            }
        }
        $('.banner_goods').html(goodsHtml);
    },
    
    // 加载分类产品
    loadCategoryProducts: function(categoryId) {
        var self = this;
        var $bannerDiv = $('.banner_div[name="' + categoryId + '"]');
        
        // 如果已经加载过，直接返回
        if ($bannerDiv.data('loaded')) {
            return;
        }
        
        API.getProductList({category_id: categoryId, limit: 8}, function(data) {
            if (data && data.productList && data.productList.length > 0) {
                var html = '<div class="banner_menu">';
                
                for (var i = 0; i < data.productList.length; i++) {
                    var product = data.productList[i];
                    var imageUrl = '';
                    if (product.singleProductImageList && product.singleProductImageList.length > 0) {
                        imageUrl = API.baseUrl + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src;
                    }
                    
                    html += '<a href="' + API.baseUrl + '/product/' + product.product_id + '">' +
                           '<img src="' + imageUrl + '">' +
                           '<span>' + product.product_name + '</span>' +
                           '</a>';
                }
                
                html += '</div>';
                $bannerDiv.html(html);
                $bannerDiv.data('loaded', true);
            }
        }, function(message) {
            console.error('获取分类产品失败：', message);
        });
    },
    
    // 初始化轮播图
    initBannerSlider: function() {
        var bannerSlider = {
            $bannerImgs: $(".banner img"),
            $sliderNav: $(".slider_nav"),
            currentIndex: 0,
            autoPlayInterval: null,
            
            init: function() {
                this.createNav();
                this.bindEvents();
                this.startAutoPlay();
            },
            
            createNav: function() {
                var navHtml = '';
                for (var i = 0; i < this.$bannerImgs.length; i++) {
                    navHtml += '<li ' + (i === 0 ? 'class="active"' : '') + '></li>';
                }
                this.$sliderNav.html(navHtml);
            },
            
            bindEvents: function() {
                var self = this;
                
                // 点击导航切换
                this.$sliderNav.on('click', 'li', function() {
                    var index = $(this).index();
                    self.slideTo(index);
                });
                
                // 鼠标悬停停止自动播放
                $(".banner").hover(
                    function() { self.stopAutoPlay(); },
                    function() { self.startAutoPlay(); }
                );
            },
            
            slideTo: function(index) {
                this.$bannerImgs.hide();
                this.$bannerImgs.eq(index).fadeIn();
                this.$sliderNav.find('li').removeClass('active');
                this.$sliderNav.find('li').eq(index).addClass('active');
                this.currentIndex = index;
            },
            
            nextSlide: function() {
                var nextIndex = (this.currentIndex + 1) % this.$bannerImgs.length;
                this.slideTo(nextIndex);
            },
            
            startAutoPlay: function() {
                var self = this;
                this.autoPlayInterval = setInterval(function() {
                    self.nextSlide();
                }, 3000);
            },
            
            stopAutoPlay: function() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                    this.autoPlayInterval = null;
                }
            }
        };
        
        bannerSlider.init();
    },
    
    // 显示错误信息
    showError: function(message) {
        console.error(message);
        // 可以使用更友好的方式显示错误，比如Toast或者提示框
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    HomeAPI.init();
});
