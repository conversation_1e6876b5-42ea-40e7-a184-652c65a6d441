package com.xq.tmall.controller.fore;

import com.xq.tmall.controller.BaseController;
import com.xq.tmall.dto.ProductDetailsDTO;
import com.xq.tmall.entity.*;
import com.xq.tmall.service.ProductDetailsService;
import com.xq.tmall.service.ReviewService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前台天猫-产品详情页
 */
@Slf4j
@Api(tags = "前台天猫-产品详情API")
@Controller
@RequiredArgsConstructor
public class ForeProductDetailsController extends BaseController {
    private final ProductDetailsService productDetailsService;
    private final UserService userService;
    private final ReviewService reviewService;

    // 转到前台天猫-产品详情页
    @ApiOperation(value = "转到前台天猫-产品详情页", notes = "转到前台天猫-产品详情页")
    @GetMapping(value = "product/{pid}")
    public String goToPage(HttpServletRequest request, Map<String, Object> map,
                           @PathVariable("pid") String pid /*产品ID*/) {
        try {
            // 解析产品ID
            Integer productId;
            try {
                productId = Integer.parseInt(pid);
            } catch (NumberFormatException e) {
                log.warn("无效的产品ID: {}", pid);
                return "redirect:/404";
            }

            // 基本验证商品是否存在
            Product product = productDetailsService.getProductBasicInfo(productId);
            if (product == null) {
                log.warn("商品不存在，产品ID: {}", pid);
                return "redirect:/404";
            }

            // 传递基本信息到JSP，用于JavaScript初始化
            map.put("productId", productId);
            map.put("productName", product.getProduct_name());

            return "fore/productDetailsPage";

        } catch (Exception e) {
            log.error("商品详情页面加载失败，产品ID: {}", pid, e);
            return "redirect:/404";
        }
    }

    // 获取商品详情数据-API接口
    @ApiOperation(value = "获取商品详情数据", notes = "获取商品详情数据")
    @ResponseBody
    @GetMapping(value = "api/products/{pid}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Map<String, Object>> getProductDetailsData(@PathVariable("pid") String pid /*产品ID*/) {
        try {
            // 解析产品ID
            Integer productId;
            try {
                productId = Integer.parseInt(pid);
            } catch (NumberFormatException e) {
                log.warn("无效的产品ID: {}", pid);
                return Result.error("无效的产品ID");
            }

            // 获取商品详情信息
            ProductDetailsDTO productDetails = productDetailsService.getProductDetails(productId);

            // 检查是否获取成功
            if (productDetails.getErrorMessage() != null) {
                log.warn("获取商品详情失败: {}", productDetails.getErrorMessage());
                return Result.error(productDetails.getErrorMessage());
            }

            // 获取产品评论信息
            Product product = productDetails.getProduct();
            product.setReviewList(reviewService.getListByProductId(productId, null));
            if (product.getReviewList() != null) {
                for (Review review : product.getReviewList()) {
                    review.setReview_user(userService.get(review.getReview_user().getUser_id()));
                }
            }

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("product", product);
            data.put("propertyList", productDetails.getPropertyList());
            data.put("loveProductList", productDetails.getRecommendedProducts());
            data.put("categoryList", productDetails.getCategoryList());
            data.put("guessNumber", productDetails.getGuessNumber());
            data.put("pageUtil", new PageUtil(0, 10).setTotal(product.getProduct_review_count()));

            return Result.success(data);

        } catch (Exception e) {
            log.error("获取商品详情数据失败，产品ID: {}", pid, e);
            return Result.error("获取商品详情失败");
        }
    }

    // 按产品ID加载产品评论列表-API接口
    @ApiOperation(value = "按产品ID加载产品评论列表", notes = "按产品ID加载产品评论列表")
    @ResponseBody
    @GetMapping(value = "api/products/{pid}/reviews", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<Review>> loadProductReviewList(@PathVariable("pid") String pid/*产品ID*/,
                                        @RequestParam Integer index/* 页数 */,
                                        @RequestParam Integer count/* 行数 */) {
        try {
            Integer productId = Integer.parseInt(pid);

            // 获取产品评论列表
            List<Review> reviewList = reviewService.getListByProductId(productId, new PageUtil(index, count));

            // 设置评论用户信息
            if (reviewList != null) {
                for (Review review : reviewList) {
                    User user = userService.get(review.getReview_user().getUser_id());
                    review.setReview_user(user);
                }
            }

            return Result.success(reviewList);

        } catch (Exception e) {
            log.error("加载产品评论列表失败，产品ID: {}", pid, e);
            return Result.error("加载评论失败");
        }
    }

    // 按产品ID加载产品属性列表-API接口
    @ApiOperation(value = "按产品ID加载产品属性列表", notes = "按产品ID加载产品属性列表")
    @ResponseBody
    @GetMapping(value = "api/products/{pid}/properties", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<Property>> loadProductPropertyList(@PathVariable("pid") String pid/*产品ID*/) {
        try {
            Integer productId = Integer.parseInt(pid);

            // 获取产品基本信息
            Product product = productDetailsService.getProductBasicInfo(productId);
            if (product == null) {
                return Result.error("产品不存在");
            }

            // 获取产品属性列表
            List<Property> propertyList = productDetailsService.getProductProperties(product);

            return Result.success(propertyList);

        } catch (Exception e) {
            log.error("加载产品属性列表失败，产品ID: {}", pid, e);
            return Result.error("加载产品属性失败");
        }
    }

    // 加载猜你喜欢列表-API接口
    @ApiOperation(value = "加载猜你喜欢列表", notes = "加载猜你喜欢列表")
    @ResponseBody
    @GetMapping(value = "api/products/recommendations", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<Map<String, Object>> guessYouLike(@RequestParam Integer categoryId, @RequestParam Integer guessNumber) {
        try {
            // 获取猜你喜欢列表
            List<Product> loveProductList = productDetailsService.getRecommendedProducts(categoryId, guessNumber,3);
            
            Map<String, Object> data = new HashMap<>();
            data.put("loveProductList", loveProductList);
            data.put("guessNumber", guessNumber + 1);
            
            return Result.success(data);
            
        } catch (Exception e) {
            log.error("加载猜你喜欢列表失败，分类ID: {}", categoryId, e);
            return Result.error("加载推荐商品失败");
        }
    }
}
