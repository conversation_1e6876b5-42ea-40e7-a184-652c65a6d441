# API接口文档

## 1. 用户认证相关接口

### 1.1 用户登录

- **URL**: `/api/auth/login`
- **方法**: POST
- **描述**: 用户登录，验证用户名和密码，返回JWT Token
- **参数**:
  - `username`: 用户名（必填）
  - `password`: 密码（必填）
  - `verifyCode`: 验证码（可选）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiJ9...",
      "user": {
        "user_id": 1,
        "user_name": "admin",
        // 其他用户信息...
      }
    },
    "timestamp": 1624512345678
  }
  ```

### 1.2 用户退出登录

- **URL**: `/api/auth/logout`
- **方法**: POST
- **描述**: 用户退出登录
- **参数**: 无
- **响应**:
  ```json
  {
    "code": 200,
    "message": "退出登录成功",
    "data": null,
    "timestamp": 1624512345678
  }
  ```

### 1.3 获取登录验证码

- **URL**: `/api/auth/code`
- **方法**: GET
- **描述**: 获取登录验证码
- **参数**: 无
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取验证码成功",
    "data": {
      "id": "1234567890",
      "imageBase64": "data:image/png;base64,...",
      "code": "abcd"
    },
    "timestamp": 1624512345678
  }
  ```

### 1.4 用户注册

- **URL**: `/api/auth/register`
- **方法**: POST
- **描述**: 用户注册
- **参数**:
  - `user_name`: 用户名（必填）
  - `user_nickname`: 用户昵称（必填）
  - `user_password`: 用户密码（必填）
  - `user_gender`: 用户性别（必填）
  - `user_birthday`: 用户生日（必填）
  - `user_address`: 用户所在地（必填）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "userId": 1,
      "userName": "username"
    },
    "timestamp": 1624512345678
  }
  ```

## 2. 用户信息相关接口

### 2.1 获取用户信息

- **URL**: `/api/users/info`
- **方法**: GET
- **描述**: 获取当前登录用户的详细信息
- **参数**: 无（需要在请求头中包含Authorization: Bearer {token}）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
      "user": {
        "user_id": 1,
        "user_name": "username",
        // 其他用户信息...
      },
      "addressList": [
        // 省份列表...
      ],
      "cityList": [
        // 城市列表...
      ],
      "districtList": [
        // 区县列表...
      ],
      "addressId": "110000",
      "cityAddressId": "110100",
      "districtAddressId": "110101"
    },
    "timestamp": 1624512345678
  }
  ```

### 2.2 更新用户信息

- **URL**: `/api/users`
- **方法**: PUT
- **描述**: 更新当前登录用户的信息
- **参数**:
  - `user_nickname`: 用户昵称（必填）
  - `user_realname`: 真实姓名（必填）
  - `user_gender`: 用户性别（必填）
  - `user_birthday`: 用户生日（必填）
  - `user_address`: 用户所在地（必填）
  - `user_profile_picture_src`: 用户头像（可选）
  - `user_password`: 用户密码（必填）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "用户信息更新成功",
    "data": {
      "user_id": 1,
      "user_name": "username",
      // 其他用户信息...
    },
    "timestamp": 1624512345678
  }
  ```

### 2.3 上传用户头像

- **URL**: `/api/users/avatar`
- **方法**: POST
- **描述**: 上传用户头像图片
- **参数**:
  - `file`: 头像图片文件（必填，multipart/form-data）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "头像上传成功",
    "data": {
      "fileName": "a1b2c3d4.jpg",
      "fileUrl": "/res/images/item/userProfilePicture/a1b2c3d4.jpg"
    },
    "timestamp": 1624512345678
  }
  ```

## 3. 地址相关接口

### 3.1 获取地址数据

- **URL**: `/api/addresses`
- **方法**: GET
- **描述**: 获取地址数据，包括省份、城市和区县
- **参数**: 无
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取地址数据成功",
    "data": {
      "addressList": [
        // 省份列表...
      ],
      "cityList": [
        // 城市列表...
      ],
      "districtList": [
        // 区县列表...
      ],
      "defaultAddressId": "110000",
      "defaultCityAddressId": "110100",
      "defaultDistrictAddressId": "110101"
    },
    "timestamp": 1624512345678
  }
  ```

### 3.2 根据上级地址ID获取下级地址列表

- **URL**: `/api/addresses/{parentId}`
- **方法**: GET
- **描述**: 根据上级地址ID获取下级地址列表
- **参数**:
  - `parentId`: 上级地址ID（路径参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取地址列表成功",
    "data": [
      {
        "address_areaId": "110101",
        "address_name": "东城区",
        // 其他地址信息...
      },
      // 更多地址...
    ],
    "timestamp": 1624512345678
  }
  ```

## 4. 商品相关接口

### 4.1 获取商品详情数据

- **URL**: `/api/products/{pid}`
- **方法**: GET
- **描述**: 获取商品详情数据
- **参数**:
  - `pid`: 产品ID（路径参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "product": {
        "product_id": 1,
        "product_name": "商品名称",
        // 其他商品信息...
      },
      "propertyList": [
        // 商品属性列表...
      ],
      "loveProductList": [
        // 推荐商品列表...
      ],
      "categoryList": [
        // 分类列表...
      ],
      "guessNumber": 1,
      "pageUtil": {
        // 分页信息...
      }
    },
    "timestamp": 1624512345678
  }
  ```

### 4.2 获取商品评论列表

- **URL**: `/api/products/{pid}/reviews`
- **方法**: GET
- **描述**: 按产品ID加载产品评论列表
- **参数**:
  - `pid`: 产品ID（路径参数）
  - `index`: 页数（查询参数）
  - `count`: 每页行数（查询参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "review_id": 1,
        "review_content": "评论内容",
        "review_user": {
          // 用户信息...
        },
        // 其他评论信息...
      },
      // 更多评论...
    ],
    "timestamp": 1624512345678
  }
  ```

### 4.3 获取商品属性列表

- **URL**: `/api/products/{pid}/properties`
- **方法**: GET
- **描述**: 按产品ID加载产品属性列表
- **参数**:
  - `pid`: 产品ID（路径参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "property_id": 1,
        "property_name": "属性名称",
        "property_value": "属性值",
        // 其他属性信息...
      },
      // 更多属性...
    ],
    "timestamp": 1624512345678
  }
  ```

### 4.4 获取推荐商品列表

- **URL**: `/api/products/recommendations`
- **方法**: GET
- **描述**: 加载猜你喜欢列表
- **参数**:
  - `categoryId`: 分类ID（查询参数）
  - `guessNumber`: 猜测数量（查询参数）
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "loveProductList": [
        {
          "product_id": 1,
          "product_name": "商品名称",
          // 其他商品信息...
        },
        // 更多推荐商品...
      ],
      "guessNumber": 2
    },
    "timestamp": 1624512345678
  }
  ```

## 5. 错误响应

所有API接口在发生错误时，都会返回统一格式的错误响应：

```json
{
  "code": 500, // 或其他错误码
  "message": "错误信息",
  "data": null,
  "timestamp": 1624512345678
}
```

常见错误码：
- 400: 请求参数错误
- 401: 未授权（未登录或登录已过期）
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误 